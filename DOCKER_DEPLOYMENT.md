# HiveChat Docker 部署指南

本指南介绍如何使用 Docker 部署 HiveChat，支持两种部署方式：
1. **独立镜像模式**：连接外部 PostgreSQL 数据库
2. **完整堆栈模式**：使用 docker-compose 同时运行应用和数据库

## 🚀 快速开始

### 方式一：独立镜像模式（推荐生产环境）

独立镜像模式允许您将 HiveChat 连接到任何外部 PostgreSQL 数据库，适合生产环境部署。

#### 1. 构建镜像

```bash
# 克隆项目
git clone https://github.com/HiveNexus/hivechat.git
cd hivechat

# 构建独立镜像
docker build -t hivechat:latest .
```

#### 2. 运行容器

```bash
# 基本运行命令
docker run -d \
  --name hivechat \
  -p 3000:3000 \
  -e DATABASE_URL="**********************************************/hivechat" \
  -e AUTH_SECRET="your-auth-secret-key" \
  -e NEXTAUTH_URL="https://your-domain.com" \
  hivechat:latest
```

#### 3. 完整配置示例

```bash
docker run -d \
  --name hivechat \
  -p 3000:3000 \
  -e DATABASE_URL="postgres://hivechat_user:<EMAIL>:5432/hivechat_prod" \
  -e AUTH_SECRET="$(openssl rand -base64 32)" \
  -e NEXTAUTH_URL="https://chat.yourdomain.com" \
  -e AUTH_TRUST_HOST="true" \
  -e AUTH_GOOGLE_ID="your-google-client-id" \
  -e AUTH_GOOGLE_SECRET="your-google-client-secret" \
  -e AUTH_GITHUB_ID="your-github-client-id" \
  -e AUTH_GITHUB_SECRET="your-github-client-secret" \
  -e NEXT_PUBLIC_AMPLITUDE_API_KEY="your-amplitude-key" \
  --restart unless-stopped \
  hivechat:latest
```

### 方式二：完整堆栈模式（适合开发和测试）

使用 docker-compose 同时运行应用和 PostgreSQL 数据库。

#### 1. 准备配置文件

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，设置必要的环境变量
nano .env
```

#### 2. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📋 环境变量配置

### 必需的环境变量

| 变量名 | 描述 | 示例 |
|--------|------|------|
| `DATABASE_URL` | PostgreSQL 数据库连接字符串 | `******************************/dbname` |
| `AUTH_SECRET` | 用于加密的密钥（32字节） | 使用 `openssl rand -base64 32` 生成 |

### 可选的环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NEXTAUTH_URL` | 应用的完整 URL | `http://localhost:3000` |
| `AUTH_TRUST_HOST` | 是否信任主机头 | `true` |
| `HOST_PORT` | 主机端口 | `3000` |
| `AUTH_GOOGLE_ID` | Google OAuth 客户端 ID | - |
| `AUTH_GOOGLE_SECRET` | Google OAuth 客户端密钥 | - |
| `AUTH_GITHUB_ID` | GitHub OAuth 客户端 ID | - |
| `AUTH_GITHUB_SECRET` | GitHub OAuth 客户端密钥 | - |
| `NEXT_PUBLIC_AMPLITUDE_API_KEY` | Amplitude 分析 API 密钥 | - |

## 🗄️ 数据库要求

### 支持的数据库版本
- PostgreSQL 12+（推荐 16+）

### 数据库连接字符串格式
```
postgres://username:password@hostname:port/database_name
```

### 示例连接字符串
```bash
# 本地数据库
DATABASE_URL="postgres://postgres:password@localhost:5432/hivechat"

# 云数据库（如 AWS RDS）
DATABASE_URL="postgres://hivechat_user:<EMAIL>:5432/hivechat"

# Neon（Serverless PostgreSQL）
DATABASE_URL="postgres://username:<EMAIL>/neondb"

# Supabase
DATABASE_URL="postgres://postgres.xyz:[password]@aws-0-us-east-1.pooler.supabase.com:5432/postgres"
```

## 🔧 高级配置

### 使用 Docker Compose 连接外部数据库

创建 `docker-compose.override.yml` 文件：

```yaml
version: '3.8'
services:
  app:
    environment:
      DATABASE_URL: "*************************************/hivechat"
```

### 健康检查

容器包含内置的健康检查，您可以通过以下方式监控：

```bash
# 检查容器健康状态
docker ps

# 查看健康检查日志
docker inspect --format='{{json .State.Health}}' hivechat
```

### 数据持久化

如果使用完整堆栈模式，数据库数据会自动持久化到 Docker 卷中：

```bash
# 查看数据卷
docker volume ls

# 备份数据卷
docker run --rm -v hivechat_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v hivechat_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/backup.tar.gz -C /data
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库连接
   docker exec hivechat pg_isready -h your-db-host -p 5432 -U username
   ```

2. **权限问题**
   ```bash
   # 检查容器日志
   docker logs hivechat
   ```

3. **端口冲突**
   ```bash
   # 使用不同端口
   docker run -p 8080:3000 ... hivechat:latest
   ```

### 调试模式

启用详细日志：

```bash
docker run -e NODE_ENV=development -e DEBUG=* hivechat:latest
```

## 📚 更多资源

- [项目主页](https://github.com/HiveNexus/hivechat)
- [问题反馈](https://github.com/HiveNexus/hivechat/issues)
- [贡献指南](https://github.com/HiveNexus/hivechat/blob/main/CONTRIBUTING.md)
