# Docker Compose 配置文件 - 独立运行模式
# 此配置文件演示如何使用独立的 HiveChat 镜像连接到外部数据库
# 
# 使用方法:
# 1. 复制此文件为 docker-compose.yml 或使用 -f 参数指定
# 2. 设置环境变量或修改下面的环境变量值
# 3. 运行: docker-compose -f docker-compose.standalone.yml up

version: '3.8'

services:
  hivechat:
    container_name: hivechat-standalone
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${HOST_PORT:-3000}:3000"
    environment:
      # 必需的环境变量
      DATABASE_URL: ${DATABASE_URL}  # 外部 PostgreSQL 数据库连接字符串
      AUTH_SECRET: ${AUTH_SECRET}    # 用于加密的密钥
      
      # 可选的环境变量
      NEXTAUTH_URL: ${NEXTAUTH_URL:-http://localhost:3000}
      AUTH_TRUST_HOST: ${AUTH_TRUST_HOST:-true}
      
      # OAuth 配置（可选）
      AUTH_GOOGLE_ID: ${AUTH_GOOGLE_ID:-}
      AUTH_GOOGLE_SECRET: ${AUTH_GOOGLE_SECRET:-}
      AUTH_GITHUB_ID: ${AUTH_GITHUB_ID:-}
      AUTH_GITHUB_SECRET: ${AUTH_GITHUB_SECRET:-}
      
      # 分析工具配置（可选）
      NEXT_PUBLIC_AMPLITUDE_API_KEY: ${NEXT_PUBLIC_AMPLITUDE_API_KEY:-}
      
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 如果需要本地测试数据库，可以取消注释以下部分
# services:
#   hivechat:
#     # ... 上面的配置 ...
#     depends_on:
#       - test-db
#   
#   test-db:
#     container_name: hivechat-test-db
#     image: postgres:16.8-alpine
#     environment:
#       POSTGRES_USER: testuser
#       POSTGRES_PASSWORD: testpass
#       POSTGRES_DB: hivechat_test
#     ports:
#       - "5432:5432"
#     volumes:
#       - test_postgres_data:/var/lib/postgresql/data
#     healthcheck:
#       test: ["CMD-SHELL", "pg_isready -U testuser -d hivechat_test"]
#       interval: 5s
#       timeout: 5s
#       retries: 5
# 
# volumes:
#   test_postgres_data:
